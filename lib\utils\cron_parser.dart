class CronParser {
  static const Map<String, String> _weekDays = {
    '0': '<PERSON>',
    '1': '<PERSON><PERSON>',
    '2': '<PERSON><PERSON>',
    '3': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '4': '<PERSON><PERSON>',
    '5': '<PERSON>ier<PERSON>',
    '6': 'Sábado',
  };

  static const Map<int, String> _months = {
    1: '<PERSON><PERSON>',
    2: 'Febrer<PERSON>',
    3: '<PERSON><PERSON>',
    4: '<PERSON>bri<PERSON>',
    5: 'Mayo',
    6: '<PERSON><PERSON>',
    7: '<PERSON>',
    8: 'Agosto',
    9: 'Septiembre',
    10: 'Octubre',
    11: 'Noviembre',
    12: 'Diciembre',
  };

  /// Parsea una expresión cron y devuelve una descripción legible
  static String parseCronExpression(String cronExpression) {
    try {
      final parts = cronExpression.trim().split(' ');
      if (parts.length != 5) {
        return 'Expresión cron inválida';
      }

      final minute = parts[0];
      final hour = parts[1];
      final dayOfMonth = parts[2];
      final month = parts[3];
      final dayOfWeek = parts[4];

      final timeStr = _formatTime(minute, hour);

      // Diario
      if (dayOfMonth == '*' && month == '*' && dayOfWeek == '*') {
        return 'Diario a las $timeStr';
      }

      // Semanal
      if (dayOfMonth == '*' && month == '*' && dayOfWeek != '*') {
        if (dayOfWeek.contains(',')) {
          final days = dayOfWeek
              .split(',')
              .map((d) => _weekDays[d] ?? d)
              .join(', ');
          return 'Los $days a las $timeStr';
        } else {
          final dayName = _weekDays[dayOfWeek] ?? dayOfWeek;
          return 'Cada $dayName a las $timeStr';
        }
      }

      // Mensual
      if (dayOfMonth != '*' && month == '*' && dayOfWeek == '*') {
        return 'El día $dayOfMonth de cada mes a las $timeStr';
      }

      // Anual
      if (dayOfMonth != '*' && month != '*' && dayOfWeek == '*') {
        final monthName = _months[int.tryParse(month)] ?? month;
        return 'El $dayOfMonth de $monthName a las $timeStr';
      }

      return 'Programación personalizada: $cronExpression';
    } catch (e) {
      return 'Error al parsear expresión cron';
    }
  }

  static String _formatTime(String minute, String hour) {
    try {
      final h = int.parse(hour);
      final m = int.parse(minute);
      final hourStr = h.toString().padLeft(2, '0');
      final minuteStr = m.toString().padLeft(2, '0');
      return '$hourStr:$minuteStr';
    } catch (e) {
      return '$hour:$minute';
    }
  }
}
