import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile, Response;
import '../models/history_model.dart';
import '../constants/api_endpoints.dart';
import 'user_provider.dart';

class HistoryProvider extends GetxController {
  late final Dio _dio;
  final UserProvider _userProvider = Get.find<UserProvider>();

  // Mapa para almacenar el historial de cada bot (botId -> lista de historiales)
  final RxMap<String, List<ChatHistory>> _historyMap =
      <String, List<ChatHistory>>{}.obs;

  // Mapa para almacenar las sugerencias temporales del último mensaje por bot
  final RxMap<String, List<String>> _temporarySuggestions =
      <String, List<String>>{}.obs;

  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _currentBotId = ''.obs;

  // Getters
  List<ChatHistory> get currentHistory {
    final history = _historyMap[_currentBotId.value] ?? [];
    return _applyTemporarySuggestions(history, _currentBotId.value);
  }

  List<ChatHistory> getHistoryForBot(String botId) {
    final history = _historyMap[botId] ?? [];
    return _applyTemporarySuggestions(history, botId);
  }

  List<String>? getTemporarySuggestions(String botId) =>
      _temporarySuggestions[botId];

  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get currentBotId => _currentBotId.value;

  // Setter para el bot actual
  set currentBotId(String botId) {
    _currentBotId.value = botId;
  }

  HistoryProvider() {
    _dio = Dio();
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(minutes: 2);
  }

  /// Obtiene el historial de mensajes para un bot específico
  Future<void> fetchHistory(String botId) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // Verificar que el usuario esté autenticado
      if (!_userProvider.isLoggedIn || _userProvider.accessToken.isEmpty) {
        throw Exception('Usuario no autenticado');
      }

      // Obtener la URL base guardada en el provider
      final baseUrl = _userProvider.baseUrl;
      if (baseUrl.isEmpty) {
        throw Exception('URL del servidor no configurada');
      }

      // Construir la URL completa
      final url = '$baseUrl${ApiEndpoints.historyEndpoint}';

      // Preparar los datos del formulario
      final formData = FormData.fromMap({'categoryId': botId});

      // Configurar headers con el token
      final headers = {'Authorization': 'Bearer ${_userProvider.accessToken}'};

      // Realizar la petición
      final response = await _dio.post(
        url,
        data: formData,
        options: Options(headers: headers),
      );

      // Verificar el código de estado
      if (response.statusCode == 200) {
        // Parsear la respuesta
        final List<dynamic> historyJson = response.data;
        final historyList = historyJson
            .map((json) => ChatHistory.fromJson(json))
            .toList();

        // Guardar el historial en el mapa
        _historyMap[botId] = historyList;

        // Si es el primer bot o el bot actual, actualizar el ID del bot actual
        if (_currentBotId.isEmpty) {
          _currentBotId.value = botId;
        }
      } else {
        throw Exception('Error en el servidor: ${response.statusCode}');
      }
    } on DioException catch (e) {
      _error.value = _handleDioError(e).toString();
    } catch (e) {
      _error.value = 'Error inesperado: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Limpia todo el historial
  void clearHistory() {
    _historyMap.clear();
    _temporarySuggestions.clear();
    _error.value = '';
    _currentBotId.value = '';
  }

  /// Limpia el historial de un bot específico
  void clearHistoryForBot(String botId) {
    _historyMap.remove(botId);
    _temporarySuggestions.remove(botId);
    if (_currentBotId.value == botId) {
      _currentBotId.value = '';
    }
  }

  /// Agrega un mensaje temporal del usuario al historial local
  void addTemporaryUserMessage(String botId, String message, String userId) {
    final currentHistory = _historyMap[botId] ?? [];

    // Crear un mensaje temporal del usuario
    final temporaryMessage = ChatHistory(
      id: -1, // ID temporal negativo
      rid: -1, // RID temporal negativo
      usuario: userId,
      usuarioDestino: userId, // Para mensajes del usuario, ambos son iguales
      mensaje: message,
      fechaRegistro: DateTime.now(),
      mensajeTipo: 'user',
      respuesta: null,
      suggestions: null,
    );

    // Agregar el mensaje temporal al final de la lista
    final updatedHistory = [...currentHistory, temporaryMessage];
    _historyMap[botId] = updatedHistory;
  }

  /// Agrega un mensaje temporal del bot al historial local
  void addTemporaryBotMessage(String botId, String response, String userId) {
    final currentHistory = _historyMap[botId] ?? [];

    // Crear un mensaje temporal del bot
    final temporaryMessage = ChatHistory(
      id: -2, // ID temporal negativo diferente para bot
      rid: -2, // RID temporal negativo
      usuario: botId, // El bot es el usuario que envía
      usuarioDestino: userId, // El usuario es el destinatario
      mensaje: 'Análisis IA', // Mensaje base
      fechaRegistro: DateTime.now(),
      mensajeTipo: 'bot',
      respuesta: response, // La respuesta del análisis
      suggestions: null,
    );

    // Agregar el mensaje temporal al final de la lista
    final updatedHistory = [...currentHistory, temporaryMessage];
    _historyMap[botId] = updatedHistory;

    // Forzar actualización de la reactividad
    _historyMap.refresh();
  }

  /// Remueve mensajes temporales (con ID negativos) del historial
  void removeTemporaryMessages(String botId) {
    final currentHistory = _historyMap[botId] ?? [];
    final filteredHistory = currentHistory
        .where((message) => message.id > 0)
        .toList();
    _historyMap[botId] = filteredHistory;

    // Forzar actualización de la reactividad
    _historyMap.refresh();
  }

  /// Remueve solo el último mensaje temporal agregado
  void removeLastTemporaryMessage(String botId) {
    final currentHistory = _historyMap[botId] ?? [];
    if (currentHistory.isNotEmpty) {
      final lastMessage = currentHistory.last;
      if (lastMessage.id < 0) {
        // Solo remover si el último mensaje es temporal
        final updatedHistory = currentHistory.sublist(
          0,
          currentHistory.length - 1,
        );
        _historyMap[botId] = updatedHistory;
        _historyMap.refresh();
      }
    }
  }

  /// Establece las sugerencias temporales para el último mensaje del bot
  void setLastMessageSuggestions(String botId, List<String> suggestions) {
    _temporarySuggestions[botId] = suggestions;
    _temporarySuggestions.refresh();
  }

  /// Limpia las sugerencias temporales para un bot
  void clearTemporarySuggestions(String botId) {
    _temporarySuggestions.remove(botId);
    _temporarySuggestions.refresh();
  }

  /// Aplica las sugerencias temporales al último mensaje con respuesta
  List<ChatHistory> _applyTemporarySuggestions(
    List<ChatHistory> history,
    String botId,
  ) {
    final suggestions = _temporarySuggestions[botId];
    if (suggestions == null || suggestions.isEmpty || history.isEmpty) {
      return history;
    }

    // Buscar el último mensaje que tenga respuesta (mensaje del bot)
    for (int i = history.length - 1; i >= 0; i--) {
      final message = history[i];
      if (message.respuesta != null &&
          message.respuesta.toString().isNotEmpty) {
        // Crear una nueva lista con el mensaje actualizado
        final updatedHistory = [...history];
        updatedHistory[i] = ChatHistory(
          id: message.id,
          rid: message.rid,
          usuario: message.usuario,
          usuarioDestino: message.usuarioDestino,
          mensaje: message.mensaje,
          fechaRegistro: message.fechaRegistro,
          mensajeTipo: message.mensajeTipo,
          respuesta: message.respuesta,
          suggestions: suggestions,
        );
        return updatedHistory;
      }
    }
    return history;
  }

  /// Actualiza el historial de forma segura, preservando mensajes temporales si es necesario
  Future<void> safeUpdateHistory(String botId) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // Verificar que el usuario esté autenticado
      if (!_userProvider.isLoggedIn || _userProvider.accessToken.isEmpty) {
        throw Exception('Usuario no autenticado');
      }

      // Obtener la URL base guardada en el provider
      final baseUrl = _userProvider.baseUrl;
      if (baseUrl.isEmpty) {
        throw Exception('URL del servidor no configurada');
      }

      // Construir la URL completa
      final url = '$baseUrl${ApiEndpoints.historyEndpoint}';

      // Preparar los datos del formulario
      final formData = FormData.fromMap({'categoryId': botId});

      // Configurar headers con el token
      final headers = {'Authorization': 'Bearer ${_userProvider.accessToken}'};

      // Realizar la petición
      final response = await _dio.post(
        url,
        data: formData,
        options: Options(headers: headers),
      );

      // Verificar el código de estado
      if (response.statusCode == 200) {
        // Parsear la respuesta
        final List<dynamic> historyJson = response.data;
        final newHistoryList = historyJson
            .map((json) => ChatHistory.fromJson(json))
            .toList();

        // Obtener historial actual y preservar mensajes temporales si existen
        final currentHistory = _historyMap[botId] ?? [];
        final temporaryMessages = currentHistory
            .where((msg) => msg.id < 0)
            .toList();

        // Si hay mensajes temporales, combinar con el nuevo historial
        if (temporaryMessages.isNotEmpty) {
          // Agregar mensajes temporales al final del nuevo historial
          final combinedHistory = [...newHistoryList, ...temporaryMessages];
          _historyMap[botId] = combinedHistory;
        } else {
          // Si no hay mensajes temporales, usar el nuevo historial directamente
          _historyMap[botId] = newHistoryList;
        }

        // Si es el primer bot o el bot actual, actualizar el ID del bot actual
        if (_currentBotId.isEmpty) {
          _currentBotId.value = botId;
        }
      } else {
        throw Exception('Error en el servidor: ${response.statusCode}');
      }
    } on DioException catch (e) {
      _error.value = _handleDioError(e).toString();
      rethrow; // Re-lanzar para que el ChatService pueda manejar el error
    } catch (e) {
      _error.value = 'Error inesperado: $e';
      rethrow; // Re-lanzar para que el ChatService pueda manejar el error
    } finally {
      _isLoading.value = false;
    }
  }

  Exception _handleDioError(DioException e) {
    if (e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.receiveTimeout ||
        e.type == DioExceptionType.sendTimeout) {
      return Exception('Tiempo de espera agotado. Verifica tu conexión.');
    } else if (e.type == DioExceptionType.connectionError) {
      return Exception('Error de conexión. Verifica tu red.');
    } else if (e.response != null) {
      final statusCode = e.response!.statusCode;
      final responseData = e.response!.data;

      if (statusCode == 401) {
        return Exception('No autorizado. Inicia sesión nuevamente.');
      } else if (statusCode == 404) {
        return Exception('Recurso no encontrado.');
      } else {
        String message = 'Error del servidor';
        if (responseData is Map && responseData.containsKey('message')) {
          message = responseData['message'];
        }
        return Exception('$message (Código: $statusCode)');
      }
    } else {
      return Exception('Error de red: ${e.message}');
    }
  }

  @override
  void onClose() {
    _dio.close();
    super.onClose();
  }
}
