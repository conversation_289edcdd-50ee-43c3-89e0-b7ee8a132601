import 'dart:io';
import 'package:csv/csv.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';

class CsvGenerator {
  /// Genera un archivo CSV a partir de una lista de mapas de datos
  static Future<File> generateCsvFile({
    required List<Map<String, dynamic>> data,
    required String fileName,
  }) async {
    if (data.isEmpty) {
      throw Exception('No hay datos para generar el archivo CSV');
    }

    try {
      // Obtener el directorio temporal
      final directory = await getTemporaryDirectory();

      // Crear el nombre del archivo con timestamp para evitar conflictos
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final csvFileName = '${fileName}_$timestamp.csv';
      final file = File('${directory.path}/$csvFileName');

      // Obtener las columnas de la primera fila
      final List<String> headers = data.first.keys.toList();

      // Crear las filas del CSV
      List<List<dynamic>> csvData = [];

      // Agregar encabezados
      csvData.add(headers);

      // Agregar datos
      for (var row in data) {
        List<dynamic> csvRow = [];
        for (var header in headers) {
          // Convertir valores a string y manejar valores nulos
          var value = row[header];
          if (value == null) {
            csvRow.add('');
          } else {
            csvRow.add(value.toString());
          }
        }
        csvData.add(csvRow);
      }

      // Convertir a CSV
      String csvString = const ListToCsvConverter().convert(csvData);

      // Escribir al archivo
      await file.writeAsString(csvString);

      if (kDebugMode) {
        print('Archivo CSV generado: ${file.path}');
        print('Número de filas: ${data.length}');
        print('Columnas: ${headers.join(', ')}');
      }

      return file;
    } catch (e) {
      if (kDebugMode) {
        print('Error al generar archivo CSV: $e');
      }
      throw Exception('Error al generar archivo CSV: $e');
    }
  }

  /// Genera un nombre de archivo basado en el contexto del mensaje
  static String generateFileName({
    required String botName,
    required DateTime messageDate,
    String? customName,
  }) {
    if (customName != null && customName.isNotEmpty) {
      return _sanitizeFileName(customName);
    }

    // Formato: BotName_YYYY-MM-DD_HH-mm
    final dateStr = messageDate
        .toIso8601String()
        .substring(0, 16)
        .replaceAll(':', '-');
    final sanitizedBotName = _sanitizeFileName(botName);

    return '${sanitizedBotName}_$dateStr';
  }

  /// Limpia el nombre del archivo removiendo caracteres no válidos
  static String _sanitizeFileName(String fileName) {
    // Remover caracteres no válidos para nombres de archivo
    return fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
        .replaceAll(' ', '_')
        .toLowerCase();
  }
}
