// lib/login_page.dart
import 'package:flutter/material.dart';
import 'package:intelibots/pages/bot_selection_page.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/auth_service.dart';
import 'package:get/get.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  String? _savedUrl;
  final TextEditingController _urlController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final AuthService _authService = AuthService();
  bool _isLoading = false;
  static const String _urlKey = 'connection_url';

  @override
  void initState() {
    super.initState();
    _loadSavedUrl();
  }

  Future<void> _loadSavedUrl() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _savedUrl = prefs.getString(_urlKey);
      _urlController.text = _savedUrl ?? ''; // Pre-fill el campo si hay URL
    });
  }

  Future<void> _saveUrl(String url) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_urlKey, url);
    setState(() {
      _savedUrl = url;
    });

    // Validar automáticamente la conexión después de guardar
    await _validateConnection(url);
  }

  Future<void> _clearUrl() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_urlKey);
    setState(() {
      _savedUrl = null;
      _urlController.text = '';
    });
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('URL eliminada')));
    }
  }

  /// Valida la conexión con el servidor
  Future<void> _validateConnection(String url) async {
    try {
      final isValid = await _authService.validateConnection(url);

      if (mounted) {
        if (isValid) {
          // Conexión exitosa - mostrar snackbar verde
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Conexión validada y URL guardada correctamente'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        } else {
          // Conexión fallida - mostrar alerta y reabrir diálogo
          _showConnectionErrorDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        _showConnectionErrorDialog();
      }
    }
  }

  /// Muestra un diálogo de error de conexión
  void _showConnectionErrorDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error de Conexión'),
          content: const Text(
            'No se pudo validar la conexión con el servidor. Por favor, verifique que la URL sea correcta y que el servidor esté disponible.',
          ),
          actions: [
            TextButton(
              child: const Text('Aceptar'),
              onPressed: () {
                Navigator.of(context).pop();
                // Reabrir el diálogo de configuración de URL
                _showUrlInputDialog(context);
              },
            ),
          ],
        );
      },
    );
  }

  void _showUrlInputDialog(BuildContext context) {
    // Asegurarse de que el controlador tenga el valor actual antes de mostrar el diálogo
    _urlController.text = _savedUrl ?? '';

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Configurar URL de Conexión'),
          content: TextField(
            controller: _urlController,
            decoration: const InputDecoration(
              hintText: 'https://tu-api.com',
              labelText: 'URL del Servidor',
            ),
            keyboardType: TextInputType.url,
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancelar'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
            if (_savedUrl != null &&
                _savedUrl!.isNotEmpty) // Mostrar solo si hay URL guardada
              TextButton(
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Eliminar URL'),
                onPressed: () {
                  _clearUrl();
                  Navigator.of(dialogContext).pop();
                },
              ),
            TextButton(
              child: const Text('Guardar'),
              onPressed: () async {
                if (_urlController.text.isNotEmpty) {
                  Navigator.of(dialogContext).pop();
                  await _saveUrl(_urlController.text);
                } else {
                  // Opcional: Mostrar error si el campo está vacío
                  ScaffoldMessenger.of(dialogContext).showSnackBar(
                    const SnackBar(
                      content: Text('La URL no puede estar vacía'),
                    ),
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  /// Método para realizar el login
  Future<void> _performLogin() async {
    final username = _usernameController.text.trim();
    final password = _passwordController.text.trim();

    // Validaciones básicas
    if (username.isEmpty) {
      _showErrorSnackBar('Por favor, ingrese su usuario');
      return;
    }

    if (password.isEmpty) {
      _showErrorSnackBar('Por favor, ingrese su contraseña');
      return;
    }

    if (_savedUrl == null || _savedUrl!.isEmpty) {
      _showErrorSnackBar('Por favor, configure la URL de conexión');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final loginResponse = await _authService.login(
        baseUrl: _savedUrl!,
        username: username,
        password: password,
      );

      if (loginResponse.isSuccess) {
        // Login exitoso
        _showSuccessSnackBar('¡Bienvenid@ ${loginResponse.user.nombre}!');

        // Navegar a la pantalla de selección de bots
        Get.offAll(() => const BotSelectionPage());
      } else {
        _showErrorSnackBar('Error en el login: ${loginResponse.status}');
      }
    } on AuthException catch (e) {
      _showErrorSnackBar(e.message);
    } catch (e) {
      _showErrorSnackBar('Error inesperado: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  void dispose() {
    _urlController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _authService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Determinar el ícono de la nube basado en si _savedUrl tiene valor
    IconData cloudIconData = (_savedUrl != null && _savedUrl!.isNotEmpty)
        ? Icons.cloud_outlined
        : Icons.cloud_off_outlined; // Ícono de nube con línea cruzada

    Color cloudIconColor = (_savedUrl != null && _savedUrl!.isNotEmpty)
        ? Colors
              .blue // O el color que prefieras para "conectado"
        : Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[400]!
        : Colors.grey[800]!;

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 20.0, top: 10.0),
            child: InkWell(
              // InkWell para hacerlo tappable
              onTap: () {
                _showUrlInputDialog(context);
              },
              customBorder: const CircleBorder(),
              child: Container(
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[800]
                      : Colors.grey[200],
                  shape: BoxShape.circle,
                ),
                child: Icon(cloudIconData, color: cloudIconColor, size: 30.0),
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              const SizedBox(height: 40),
              // Logo de la aplicación
              Container(
                height: 180, // Ajustado un poco para dar más espacio al título
                width: 180,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Image.asset(
                  'assets/images/login.png', // Asegúrate que esta ruta sea correcta
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(height: 16.0),
              // Título de la aplicación
              Text(
                'Intelibots',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 32.0,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
                ),
              ),
              const SizedBox(height: 32.0),
              // Campo de Usuario
              TextField(
                textInputAction: TextInputAction.next,
                controller: _usernameController,
                enabled: !_isLoading,
                decoration: InputDecoration(
                  hintText: 'Usuario',
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 15,
                    horizontal: 20,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[600]!
                          : Colors.black54,
                      width: 2,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[600]!
                          : Colors.black54,
                      width: 2,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: const BorderSide(color: Colors.blue, width: 2),
                  ),
                ),
              ),
              const SizedBox(height: 24.0),
              // Campo de Contraseña
              TextField(
                textInputAction: TextInputAction.done,
                controller: _passwordController,
                enabled: !_isLoading,
                obscureText: true,
                onSubmitted: (_) => _performLogin(),
                decoration: InputDecoration(
                  hintText: 'Contraseña',
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 15,
                    horizontal: 20,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[600]!
                          : Colors.black54,
                      width: 2,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[600]!
                          : Colors.black54,
                      width: 2,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: const BorderSide(color: Colors.blue, width: 2),
                  ),
                ),
              ),
              const SizedBox(height: 32.0),
              // Botón de Iniciar Sesión
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isLoading
                      ? Colors.grey[400]
                      : Colors.grey[300],
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  textStyle: const TextStyle(fontSize: 18),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    side: const BorderSide(color: Colors.black54, width: 2),
                  ),
                  elevation: 0,
                ),
                onPressed: _isLoading ? null : _performLogin,
                child: _isLoading
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.black54,
                              ),
                            ),
                          ),
                          SizedBox(width: 10),
                          Text(
                            'Iniciando sesión...',
                            style: TextStyle(color: Colors.black54),
                          ),
                        ],
                      )
                    : const Text(
                        'Iniciar Sesión',
                        style: TextStyle(color: Colors.black87),
                      ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
