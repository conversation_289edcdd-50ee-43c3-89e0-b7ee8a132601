import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../models/history_model.dart';
import '../../utils/response_formatter.dart';

class MessageTableWidget extends StatefulWidget {
  final ChatHistory message;
  final bool showAllRows;

  const MessageTableWidget({
    super.key,
    required this.message,
    this.showAllRows = false,
  });

  @override
  State<MessageTableWidget> createState() => _MessageTableWidgetState();
}

class _MessageTableWidgetState extends State<MessageTableWidget> {
  int? _sortColumnIndex;
  bool _sortAscending = true;
  List<Map<String, dynamic>> _originalData = [];
  List<Map<String, dynamic>> _filteredData = [];
  final Map<String, Set<dynamic>> _selectedFilterValues = {};
  String? _currentSortColumn; // Stores the name of the column currently sorted
  int _sortOrder = 0; // 0: none, 1: ascending, 2: descending

  @override
  void initState() {
    super.initState();
    _originalData = ResponseFormatter.formatResponse(widget.message);
    _filteredData = List.from(_originalData);
    _initializeFilters();
  }

  void _initializeFilters() {
    if (_originalData.isNotEmpty) {
      for (String column in _originalData.first.keys) {
        _selectedFilterValues[column] = LinkedHashSet<dynamic>();
      }
    }
  }

  void _sortData(String column, int columnIndex) {
    setState(() {
      if (_currentSortColumn == column) {
        // Cycle through sort states for the same column
        _sortOrder = (_sortOrder + 1) % 3; // 0 -> 1 -> 2 -> 0
      } else {
        // New column clicked, start with ascending sort
        _currentSortColumn = column;
        _sortOrder = 1; // Ascending
      }

      _sortColumnIndex = columnIndex;
      _sortAscending = (_sortOrder == 1);

      if (_sortOrder == 0) {
        // Reset to original order
        _filteredData = List.from(_originalData);
      } else {
        // Apply sorting
        _filteredData.sort((a, b) {
          final aValue = a[column];
          final bValue = b[column];

          // Handle null values gracefully
          if (aValue == null && bValue == null) return 0;
          if (aValue == null) return _sortAscending ? -1 : 1;
          if (bValue == null) return _sortAscending ? 1 : -1;

          // Attempt to compare as Comparable
          if (aValue is Comparable && bValue is Comparable) {
            return _sortAscending
                ? Comparable.compare(aValue, bValue)
                : Comparable.compare(bValue, aValue);
          }

          // Fallback to string comparison if not comparable
          return _sortAscending
              ? aValue.toString().toLowerCase().compareTo(
                  bValue.toString().toLowerCase(),
                )
              : bValue.toString().toLowerCase().compareTo(
                  aValue.toString().toLowerCase(),
                );
        });
      }
    });
  }

  void _applyFilters() {
    _filteredData = _originalData.where((row) {
      for (String column in _selectedFilterValues.keys) {
        final selectedValues = _selectedFilterValues[column];
        if (selectedValues != null && selectedValues.isNotEmpty) {
          final rowValue = row[column];
          if (!selectedValues.any((filterValue) {
            // Para valores numéricos, usar una comparación exacta.
            if (rowValue is num && filterValue is num) {
              return rowValue == filterValue;
            }
            // Para otros tipos (como texto), mantener la comparación "contains".
            return rowValue
                    ?.toString()
                    .toLowerCase()
                    .contains(filterValue.toString().toLowerCase()) ??
                false;
          })) {
            return false;
          }
        }
      }
      return true;
    }).toList();
    // Re-apply sort after filtering if a column is currently sorted
    if (_currentSortColumn != null && _sortOrder != 0) {
      _sortData(_currentSortColumn!, _sortColumnIndex!);
    }
  }

  // Removed _getComparableField as it's no longer directly used by _sortData

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    try {
      if (_originalData.isEmpty) {
        return const Text('No hay datos para mostrar en la tabla');
      }

      final List<String> columns = _originalData.first.keys.toList();

      final List<DataColumn> dataColumns = columns.map((column) {
        return DataColumn(
          label: Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    column,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                    overflow:
                        TextOverflow.ellipsis, // Add ellipsis for overflow
                  ),
                ),
                SizedBox(
                  width: 24, // Adjust width as needed for the filter icon
                  child: _buildFilterDropdown(column),
                ),
              ],
            ),
          ),
          onSort: (int columnIndex, bool ascending) {
            _sortData(column, columnIndex);
          },
          numeric: false,
        );
      }).toList();

      final int totalRows = _filteredData.length;
      final int visibleRows = widget.showAllRows
          ? totalRows
          : (totalRows > 10 ? 10 : totalRows);

      final List<DataRow> dataRows = _filteredData
          .take(visibleRows)
          .map(
            (row) => DataRow(
              cells: columns
                  .map(
                    (column) => DataCell(Text(row[column]?.toString() ?? '')),
                  )
                  .toList(),
            ),
          )
          .toList();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columns: dataColumns,
              rows: dataRows,
              sortColumnIndex: _sortOrder == 0 ? null : _sortColumnIndex,
              sortAscending: _sortAscending,
            ),
          ),
          if (!widget.showAllRows && totalRows > 10)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                'Mostrando $visibleRows de $totalRows filas',
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  color: theme.colorScheme.onSecondaryContainer.withValues(
                    alpha: 0.7,
                  ),
                ),
              ),
            ),
        ],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al construir tabla: $e');
        print('Tipo de mensaje: ${widget.message.mensajeTipo}');
        print('Tipo de respuesta: ${widget.message.respuesta.runtimeType}');
        print('Contenido de respuesta: ${widget.message.respuesta}');
      }

      return Text(
        'Error al mostrar la tabla: $e',
        style: TextStyle(color: theme.colorScheme.error),
      );
    }
  }

  Widget _buildFilterDropdown(String column) {
    final uniqueValues =
        _originalData
            .map((row) => row[column])
            .where((value) => value != null)
            .toSet()
            .toList()
          ..sort(
            (a, b) => a.toString().toLowerCase().compareTo(
              b.toString().toLowerCase(),
            ),
          );

    return PopupMenuButton<dynamic>(
      icon: Icon(
        Icons.filter_list,
        size: 16,
        color: _selectedFilterValues[column]!.isNotEmpty
            ? Colors.blue
            : Colors.grey,
      ),
      itemBuilder: (BuildContext context) {
        return [
          PopupMenuItem(enabled: false, child: Text('Filtrar por $column')),
          const PopupMenuDivider(),
          ...uniqueValues.map((value) {
            final isSelected = _selectedFilterValues[column]!.contains(value);
            return CheckedPopupMenuItem<dynamic>(
              value: value,
              checked: isSelected,
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedFilterValues[column]!.remove(value);
                  } else {
                    _selectedFilterValues[column]!.add(value);
                  }
                  _applyFilters();
                });
              },
              child: Text(value.toString()),
            );
          }),
          if (_selectedFilterValues[column]!.isNotEmpty)
            const PopupMenuDivider(),
          if (_selectedFilterValues[column]!.isNotEmpty)
            PopupMenuItem(
              child: const Text('Limpiar filtro'),
              onTap: () {
                setState(() {
                  _selectedFilterValues[column]!.clear();
                  _applyFilters();
                });
              },
            ),
        ];
      },
    );
  }
}
