import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart' hide FormData, MultipartFile, Response;
import '../models/scheduled_task_model.dart';
import '../providers/user_provider.dart';
import '../constants/api_endpoints.dart';

class SchedulerService extends GetxService {
  late final Dio _dio;
  final UserProvider _userProvider = Get.find<UserProvider>();

  SchedulerService() {
    _dio = Dio();
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(minutes: 2);
  }

  /// Crea una nueva tarea programada
  Future<bool> createScheduledTask({
    required String message,
    required String botName,
    required ScheduleData scheduleData,
  }) async {
    try {
      // Verificar que el usuario esté autenticado
      if (!_userProvider.isLoggedIn || _userProvider.accessToken.isEmpty) {
        throw SchedulerException('Usuario no autenticado');
      }

      // Obtener la URL base
      final baseUrl = _userProvider.baseUrl;
      if (baseUrl.isEmpty) {
        throw SchedulerException('URL del servidor no configurada');
      }

      // Construir la URL completa
      final url = '$baseUrl${ApiEndpoints.cronEndpoint}';

      // Generar expresión cron
      final cronExpression = scheduleData.generateCronExpression();

      // Preparar los datos del formulario
      final formData = FormData.fromMap({
        'frequency': cronExpression,
        'message': message,
        'bot': botName,
        //'email': _userProvider.currentUser?.usuario ?? '',
        if (scheduleData.notificationTitle != null)
          'notification_title': scheduleData.notificationTitle,
      });

      // Realizar la petición
      final response = await _dio.post(
        url,
        data: formData,
        options: Options(
          headers: {'Authorization': 'Bearer ${_userProvider.accessToken}'},
        ),
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        throw SchedulerException('Error del servidor: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Error de red al crear tarea programada: $e');
      }
      throw SchedulerException(_handleDioError(e));
    } catch (e) {
      if (kDebugMode) {
        print('Error al crear tarea programada: $e');
      }
      rethrow;
    }
  }

  /// Obtiene todas las tareas programadas
  Future<List<ScheduledTask>> getScheduledTasks() async {
    try {
      // Verificar que el usuario esté autenticado
      if (!_userProvider.isLoggedIn || _userProvider.accessToken.isEmpty) {
        throw SchedulerException('Usuario no autenticado');
      }

      // Obtener la URL base
      final baseUrl = _userProvider.baseUrl;
      if (baseUrl.isEmpty) {
        throw SchedulerException('URL del servidor no configurada');
      }

      // Construir la URL completa
      final url = '$baseUrl${ApiEndpoints.cronEndpoint}';

      // Realizar la petición
      final response = await _dio.get(
        url,
        options: Options(
          headers: {'Authorization': 'Bearer ${_userProvider.accessToken}'},
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 'success') {
          final List<dynamic> tasksJson = data['tasks'] ?? [];
          return tasksJson.map((json) => ScheduledTask.fromJson(json)).toList();
        } else {
          throw SchedulerException('Error en la respuesta del servidor');
        }
      } else {
        throw SchedulerException('Error del servidor: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Error de red al obtener tareas programadas: $e');
      }
      throw SchedulerException(_handleDioError(e));
    } catch (e) {
      if (kDebugMode) {
        print('Error al obtener tareas programadas: $e');
      }
      rethrow;
    }
  }

  /// Elimina una tarea programada
  Future<bool> deleteScheduledTask(String taskId) async {
    try {
      // Verificar que el usuario esté autenticado
      if (!_userProvider.isLoggedIn || _userProvider.accessToken.isEmpty) {
        throw SchedulerException('Usuario no autenticado');
      }

      // Obtener la URL base
      final baseUrl = _userProvider.baseUrl;
      if (baseUrl.isEmpty) {
        throw SchedulerException('URL del servidor no configurada');
      }

      // Construir la URL completa
      final url = '$baseUrl${ApiEndpoints.deleteCronEndpoint(taskId)}';

      // Realizar la petición
      final response = await _dio.delete(
        url,
        options: Options(
          headers: {'Authorization': 'Bearer ${_userProvider.accessToken}'},
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data['status'] == 'success';
      } else {
        throw SchedulerException('Error del servidor: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Error de red al eliminar tarea programada: $e');
      }
      throw SchedulerException(_handleDioError(e));
    } catch (e) {
      if (kDebugMode) {
        print('Error al eliminar tarea programada: $e');
      }
      rethrow;
    }
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Tiempo de conexión agotado';
      case DioExceptionType.sendTimeout:
        return 'Tiempo de envío agotado';
      case DioExceptionType.receiveTimeout:
        return 'Tiempo de recepción agotado';
      case DioExceptionType.badResponse:
        return 'Respuesta del servidor inválida: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Petición cancelada';
      case DioExceptionType.connectionError:
        return 'Error de conexión';
      default:
        return 'Error de red desconocido';
    }
  }
}

class SchedulerException implements Exception {
  final String message;
  SchedulerException(this.message);

  @override
  String toString() => 'SchedulerException: $message';
}
