import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/bot_model.dart';
import '../providers/bot_provider.dart';
import '../providers/history_provider.dart';
import '../pages/chat_page.dart';
import '../widgets/profile_drawer.dart';
import '../widgets/profile_avatar_button.dart';

class BotSelectionPage extends StatefulWidget {
  const BotSelectionPage({super.key});

  @override
  State<BotSelectionPage> createState() => _BotSelectionPageState();
}

class _BotSelectionPageState extends State<BotSelectionPage> {
  final BotProvider _botService = BotProvider();
  final HistoryProvider _historyProvider = Get.find<HistoryProvider>();
  final RxList<Bot> _bots = <Bot>[].obs;
  final RxBool _isLoading = true.obs;
  final RxString _error = ''.obs;

  @override
  void initState() {
    super.initState();
    _loadBots();
  }

  Future<void> _loadBots() async {
    try {
      _isLoading.value = true;
      _error.value = '';
      final bots = await _botService.getBots();
      _bots.assignAll(bots);

      // Precargar el historial de todos los bots
      if (bots.isNotEmpty) {
        await _preloadAllBotsHistory(bots);
      }
    } catch (e) {
      _error.value = e.toString();
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _preloadAllBotsHistory(List<Bot> bots) async {
    // Crear una lista de futures para cargar el historial de todos los bots
    final futures = <Future>[];

    for (final bot in bots) {
      futures.add(_loadHistoryForBot(bot.id.toString()));
    }

    // Esperar a que se complete la carga de todos los historiales
    await Future.wait(futures);

    debugPrint('Historial de todos los bots precargado');
  }

  Future<void> _loadHistoryForBot(String botId) async {
    try {
      await _historyProvider.fetchHistory(botId);
    } catch (e) {
      // Podemos manejar errores específicos del historial aquí
      // o simplemente ignorarlos para no interrumpir la carga de bots
      debugPrint('Error al cargar historial: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Bots'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false, // Para no mostrar el botón de atrás
        actions: const [ProfileAvatarButton()],
      ),
      endDrawer: const ProfileDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Obx(() {
          if (_isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_error.value.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error al cargar los bots',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(_error.value),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadBots,
                    child: const Text('Reintentar'),
                  ),
                ],
              ),
            );
          }

          if (_bots.isEmpty) {
            return Center(
              child: Text(
                'No hay bots disponibles',
                style: theme.textTheme.titleMedium,
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: _loadBots,
            child: ListView.separated(
              itemCount: _bots.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                final bot = _bots[index];
                return Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: InkWell(
                    onTap: () => _onBotSelected(context, bot),
                    borderRadius: BorderRadius.circular(12.0),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 20.0,
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10.0),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.secondaryContainer
                                  .withValues(alpha: 0.5),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: Icon(
                              bot.icon,
                              size: 28,
                              color: theme.colorScheme.onSecondaryContainer,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  bot.name,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Tipo: ${bot.type}',
                                  style: theme.textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            size: 16,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        }),
      ),
    );
  }

  void _onBotSelected(BuildContext context, Bot bot) {
    // Establecer el bot actual en el HistoryProvider
    _historyProvider.currentBotId = bot.id.toString();

    // Navegar a la pantalla de chat
    Get.to(() => ChatPage(bot: bot));
  }
}
