import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/history_model.dart';
import '../../controllers/chat_controller.dart';
import '../../services/share_service.dart';
import '../../utils/response_formatter.dart';
import 'ai_analysis_dialog.dart';
import '../../pages/full_table_page.dart'; // Added import

class BotMessageOptionsSheet extends StatelessWidget {
  final ChatHistory message;
  final String botId;

  const BotMessageOptionsSheet({
    super.key,
    required this.message,
    required this.botId,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ChatController>(tag: botId);

    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Compartir'),
            onTap: () {
              Navigator.pop(context);
              _handleShare(controller, context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.copy),
            title: const Text('Copiar al portapapeles'),
            onTap: () {
              Navigator.pop(context);
              ShareService.copyToClipboard(
                message.respuesta.toString(),
                context,
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.psychology),
            title: const Text('Análisis IA'),
            onTap: () {
              Navigator.pop(context);
              _showAiAnalysisDialog(context, controller);
            },
          ),
          if (ResponseFormatter.hasValidTableData(message)) ...[
            ListTile(
              leading: const Icon(Icons.bar_chart),
              title: const Text('Ver gráfica'),
              onTap: () {
                Navigator.pop(context);
                _navigateToChartPage(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('Ver tabla completa'),
              onTap: () {
                Navigator.pop(context);
                _navigateToFullTablePage(context);
              },
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _handleShare(
    ChatController controller,
    BuildContext context,
  ) async {
    try {
      if (message.mensajeTipo == 'TablaJSON') {
        await ShareService.shareTableAsCSV(message, controller.bot, context);
      } else {
        await ShareService.shareText(
          message.respuesta.toString(),
          controller.bot.name,
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al compartir: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToChartPage(BuildContext context) {
    Get.toNamed('/chart', arguments: message);
  }

  void _navigateToFullTablePage(BuildContext context) {
    Get.to(() => FullTablePage(message: message));
  }

  void _showAiAnalysisDialog(BuildContext context, ChatController controller) {
    showDialog(
      context: context,
      builder: (context) =>
          AiAnalysisDialog(message: message, botCategory: controller.bot.botId),
    );
  }
}
